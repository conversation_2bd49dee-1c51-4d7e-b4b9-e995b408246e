"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/set-cookie-parser";
exports.ids = ["vendor-chunks/set-cookie-parser"];
exports.modules = {

/***/ "(rsc)/./node_modules/set-cookie-parser/lib/set-cookie.js":
/*!**********************************************************!*\
  !*** ./node_modules/set-cookie-parser/lib/set-cookie.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else if (key === \"partitioned\") {\n      cookie.partitioned = true;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/set-cookie-parser/lib/set-cookie.js\n");

/***/ })

};
;