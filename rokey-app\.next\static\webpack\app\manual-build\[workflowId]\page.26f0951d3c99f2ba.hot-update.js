"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/MemoryNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemoryNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleStackIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MemoryNode(param) {\n    let { data, id } = param;\n    _s();\n    const config = data.config;\n    const memoryName = config === null || config === void 0 ? void 0 : config.memoryName;\n    const maxSize = (config === null || config === void 0 ? void 0 : config.maxSize) || 10240; // 10MB default\n    const encryption = (config === null || config === void 0 ? void 0 : config.encryption) !== false; // Default true\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges)();\n    // Find connected nodes\n    const connectedNodes = edges.filter((edge)=>edge.source === id).map((edge)=>edge.target);\n    const [memoryStats, setMemoryStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MemoryNode.useEffect\": ()=>{\n            // Fetch real memory stats from MemoryService\n            const fetchMemoryStats = {\n                \"MemoryNode.useEffect.fetchMemoryStats\": async ()=>{\n                    if (!memoryName) {\n                        setMemoryStats(null);\n                        return;\n                    }\n                    try {\n                        // TODO: Implement actual API call to fetch memory stats\n                        // For now, show empty state since no real data exists\n                        const response = await fetch(\"/api/memory/stats?memoryName=\".concat(encodeURIComponent(memoryName), \"&nodeId=\").concat(id));\n                        if (response.ok) {\n                            const stats = await response.json();\n                            setMemoryStats(stats);\n                        } else {\n                            // No data exists yet - show empty state\n                            setMemoryStats({\n                                entriesCount: 0,\n                                totalSize: '0MB',\n                                lastUpdate: 'Never'\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch memory stats:', error);\n                        // Show empty state on error\n                        setMemoryStats({\n                            entriesCount: 0,\n                            totalSize: '0MB',\n                            lastUpdate: 'Never'\n                        });\n                    }\n                }\n            }[\"MemoryNode.useEffect.fetchMemoryStats\"];\n            fetchMemoryStats();\n        }\n    }[\"MemoryNode.useEffect\"], [\n        memoryName,\n        maxSize,\n        id\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#ec4899\",\n        hasInput: false,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: memoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: [\n                            \"\\uD83E\\uDDE0 \",\n                            memoryName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Max: \",\n                            Math.round(maxSize / 1024),\n                            \"MB | \",\n                            encryption ? '🔒 Encrypted' : '🔓 Plain'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, this),\n                    memoryStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    memoryStats.entriesCount,\n                                    \" entries\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCBE \",\n                                    memoryStats.totalSize,\n                                    \" used\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDD52 Updated \",\n                                    memoryStats.lastUpdate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 15\n                    }, this),\n                    connectedNodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"\\uD83D\\uDD17 Connected to \",\n                            connectedNodes.length,\n                            \" node\",\n                            connectedNodes.length > 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded\",\n                        children: \"✅ Active & Ready\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 80,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"\\uD83E\\uDDE0 Plug & Play Memory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 108,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(MemoryNode, \"OnhTheIx/6azcxZ0B54c5UycjQo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges\n    ];\n});\n_c = MemoryNode;\nvar _c;\n$RefreshReg$(_c, \"MemoryNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\n"));

/***/ })

});