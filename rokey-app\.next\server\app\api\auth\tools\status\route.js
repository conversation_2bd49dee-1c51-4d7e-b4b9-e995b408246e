/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/tools/status/route";
exports.ids = ["app/api/auth/tools/status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_tools_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/tools/status/route.ts */ \"(rsc)/./src/app/api/auth/tools/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/tools/status/route\",\n        pathname: \"/api/auth/tools/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/tools/status/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\tools\\\\status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_tools_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/tools/status/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/auth/tools/status/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_oauth_tokenManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/oauth/tokenManager */ \"(rsc)/./src/lib/oauth/tokenManager.ts\");\n/* harmony import */ var _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/oauth/config */ \"(rsc)/./src/lib/oauth/config.ts\");\n// Tool Connection Status API\n// Get connection status for all tools or specific tool for the authenticated user\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log('🔐 TOOL STATUS: Getting tool connection status');\n        // Get authenticated user\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('🔐 TOOL STATUS: User not authenticated:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Get tool type from query params (optional)\n        const { searchParams } = new URL(request.url);\n        const toolType = searchParams.get('tool');\n        // Get all tool connections for the user\n        const connections = await (0,_lib_oauth_tokenManager__WEBPACK_IMPORTED_MODULE_2__.getUserToolConnections)(user.id);\n        if (toolType) {\n            // Return status for specific tool\n            const connection = connections.find((conn)=>conn.tool_type === toolType);\n            const toolStatus = {\n                tool_type: toolType,\n                display_name: _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DISPLAY_NAMES[toolType] || toolType,\n                icon: _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_ICONS[toolType] || '🔧',\n                description: _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DESCRIPTIONS[toolType] || 'External tool integration',\n                is_connected: !!connection,\n                connection_status: connection?.connection_status || 'disconnected',\n                provider_user_email: connection?.provider_user_email,\n                provider_user_name: connection?.provider_user_name,\n                last_used_at: connection?.last_used_at,\n                connected_at: connection?.created_at\n            };\n            console.log(`🔐 TOOL STATUS: Status for ${toolType}:`, toolStatus.is_connected);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(toolStatus);\n        }\n        // Return status for all tools\n        const allTools = Object.keys(_lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DISPLAY_NAMES);\n        const toolStatuses = allTools.map((tool)=>{\n            const connection = connections.find((conn)=>conn.tool_type === tool);\n            return {\n                tool_type: tool,\n                display_name: _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DISPLAY_NAMES[tool],\n                icon: _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_ICONS[tool],\n                description: _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DESCRIPTIONS[tool],\n                is_connected: !!connection,\n                connection_status: connection?.connection_status || 'disconnected',\n                provider_user_email: connection?.provider_user_email,\n                provider_user_name: connection?.provider_user_name,\n                last_used_at: connection?.last_used_at,\n                connected_at: connection?.created_at\n            };\n        });\n        console.log(`🔐 TOOL STATUS: Retrieved status for ${toolStatuses.length} tools`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            tools: toolStatuses,\n            connected_count: connections.length,\n            total_count: allTools.length\n        });\n    } catch (error) {\n        console.error('🔐 TOOL STATUS: Error getting tool status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get tool status'\n        }, {\n            status: 500\n        });\n    }\n}\n// Disconnect a tool\nasync function DELETE(request) {\n    try {\n        console.log('🔐 TOOL STATUS: Disconnecting tool');\n        // Get authenticated user\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('🔐 TOOL STATUS: User not authenticated:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Get tool type from query params\n        const { searchParams } = new URL(request.url);\n        const toolType = searchParams.get('tool');\n        if (!toolType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Tool type is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`🔐 TOOL STATUS: Disconnecting ${toolType} for user ${user.id}`);\n        // Delete the connection\n        const { error: deleteError } = await supabase.from('tool_oauth_connections').delete().eq('user_id', user.id).eq('tool_type', toolType);\n        if (deleteError) {\n            console.error('🔐 TOOL STATUS: Error disconnecting tool:', deleteError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to disconnect tool'\n            }, {\n                status: 500\n            });\n        }\n        console.log(`🔐 TOOL STATUS: Successfully disconnected ${toolType}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `${_lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DISPLAY_NAMES[toolType] || toolType} disconnected successfully`\n        });\n    } catch (error) {\n        console.error('🔐 TOOL STATUS: Error disconnecting tool:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to disconnect tool'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/tools/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/oauth/config.ts":
/*!*********************************!*\
  !*** ./src/lib/oauth/config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_DESCRIPTIONS: () => (/* binding */ TOOL_DESCRIPTIONS),\n/* harmony export */   TOOL_DISPLAY_NAMES: () => (/* binding */ TOOL_DISPLAY_NAMES),\n/* harmony export */   TOOL_EMOJIS: () => (/* binding */ TOOL_EMOJIS),\n/* harmony export */   TOOL_ICONS: () => (/* binding */ TOOL_ICONS),\n/* harmony export */   generateAuthUrl: () => (/* binding */ generateAuthUrl),\n/* harmony export */   getOAuthConfigForTool: () => (/* binding */ getOAuthConfigForTool),\n/* harmony export */   getToolOAuthConfigs: () => (/* binding */ getToolOAuthConfigs),\n/* harmony export */   validateOAuthConfig: () => (/* binding */ validateOAuthConfig)\n/* harmony export */ });\n// OAuth Configuration for Tool Integrations\n// This file contains OAuth configurations for all supported tools\n// Get the base URL for redirects\nconst getBaseUrl = ()=>{\n    if (false) {}\n    // Server-side detection\n    if (false) {}\n    return process.env.NEXTAUTH_URL || 'http://localhost:3000';\n};\n// Google OAuth configuration for tools\nconst getGoogleToolsConfig = ()=>({\n        clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',\n        clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',\n        authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n        tokenUrl: 'https://oauth2.googleapis.com/token',\n        scopes: [\n            'https://www.googleapis.com/auth/drive',\n            'https://www.googleapis.com/auth/documents',\n            'https://www.googleapis.com/auth/spreadsheets',\n            'https://www.googleapis.com/auth/gmail.modify',\n            'https://www.googleapis.com/auth/calendar',\n            'https://www.googleapis.com/auth/youtube'\n        ],\n        redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/google/callback`,\n        additionalParams: {\n            access_type: 'offline',\n            prompt: 'consent',\n            include_granted_scopes: 'true'\n        }\n    });\n// Notion OAuth configuration\nconst getNotionConfig = ()=>({\n        clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',\n        clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',\n        authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',\n        tokenUrl: 'https://api.notion.com/v1/oauth/token',\n        scopes: [],\n        redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/notion/callback`,\n        additionalParams: {\n            owner: 'user',\n            response_type: 'code'\n        }\n    });\n// Tool-specific OAuth configurations\nconst getToolOAuthConfigs = ()=>{\n    const googleConfig = getGoogleToolsConfig();\n    return {\n        // Google services all use the same OAuth config with different scopes\n        google_drive: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/drive'\n            ]\n        },\n        google_docs: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/documents'\n            ]\n        },\n        google_sheets: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/spreadsheets'\n            ]\n        },\n        gmail: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/gmail.modify'\n            ]\n        },\n        calendar: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/calendar'\n            ]\n        },\n        youtube: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/youtube'\n            ]\n        },\n        notion: getNotionConfig(),\n        supabase: {\n            clientId: '',\n            clientSecret: '',\n            authorizationUrl: '',\n            tokenUrl: '',\n            scopes: [],\n            redirectUri: '',\n            additionalParams: {}\n        }\n    };\n};\n// Get OAuth config for a specific tool\nconst getOAuthConfigForTool = (toolType)=>{\n    const configs = getToolOAuthConfigs();\n    return configs[toolType] || null;\n};\n// Validate OAuth configuration\nconst validateOAuthConfig = (config)=>{\n    return !!(config.clientId && config.clientSecret && config.authorizationUrl && config.tokenUrl && config.redirectUri);\n};\n// Generate OAuth authorization URL\nconst generateAuthUrl = (toolType, state)=>{\n    const config = getOAuthConfigForTool(toolType);\n    if (!config || !validateOAuthConfig(config)) {\n        return null;\n    }\n    const params = new URLSearchParams({\n        client_id: config.clientId,\n        redirect_uri: config.redirectUri,\n        response_type: 'code',\n        scope: config.scopes.join(' '),\n        state,\n        ...config.additionalParams\n    });\n    return `${config.authorizationUrl}?${params.toString()}`;\n};\n// Tool display names\nconst TOOL_DISPLAY_NAMES = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    google_sheets: 'Google Sheets',\n    gmail: 'Gmail',\n    calendar: 'Google Calendar',\n    youtube: 'YouTube',\n    notion: 'Notion',\n    supabase: 'Supabase'\n};\n// Tool icons - Professional logos from reliable sources\nconst TOOL_ICONS = {\n    google_drive: 'https://cloud.gmelius.com/public/logos/google/Google_Drive_Logo.svg',\n    google_docs: 'https://cloud.gmelius.com/public/logos/google/Google_Docs_Logo.svg',\n    google_sheets: 'https://cloud.gmelius.com/public/logos/google/Google_Sheets_Logo.svg',\n    gmail: 'https://cloud.gmelius.com/public/logos/google/Gmail_Logo.svg',\n    calendar: 'https://cloud.gmelius.com/public/logos/google/Google_Calendar_Logo.svg',\n    youtube: 'https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg',\n    notion: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png',\n    supabase: 'https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/supabase.svg'\n};\n// Tool emoji fallbacks for places where images can't be used (like select options)\nconst TOOL_EMOJIS = {\n    google_drive: '📁',\n    google_docs: '📄',\n    google_sheets: '📊',\n    gmail: '📧',\n    calendar: '📅',\n    youtube: '📺',\n    notion: '📝',\n    supabase: '🗄️'\n};\n// Tool descriptions\nconst TOOL_DESCRIPTIONS = {\n    google_drive: 'Access and manage Google Drive files',\n    google_docs: 'Create and edit Google Documents',\n    google_sheets: 'Work with Google Spreadsheets',\n    gmail: 'Send and manage emails',\n    calendar: 'Manage calendar events and schedules',\n    youtube: 'Access YouTube data and analytics',\n    notion: 'Access Notion databases and pages',\n    supabase: 'Direct database operations'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/oauth/tokenManager.ts":
/*!***************************************!*\
  !*** ./src/lib/oauth/tokenManager.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOAuthTokens: () => (/* binding */ getOAuthTokens),\n/* harmony export */   getUserToolConnections: () => (/* binding */ getUserToolConnections),\n/* harmony export */   getValidAccessToken: () => (/* binding */ getValidAccessToken),\n/* harmony export */   needsTokenRefresh: () => (/* binding */ needsTokenRefresh),\n/* harmony export */   refreshOAuthToken: () => (/* binding */ refreshOAuthToken),\n/* harmony export */   revokeOAuthConnection: () => (/* binding */ revokeOAuthConnection),\n/* harmony export */   storeOAuthTokens: () => (/* binding */ storeOAuthTokens),\n/* harmony export */   updateConnectionStatus: () => (/* binding */ updateConnectionStatus),\n/* harmony export */   updateLastUsed: () => (/* binding */ updateLastUsed)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/oauth/config.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n// OAuth Token Management\n// Handles secure storage, retrieval, and refresh of OAuth tokens\n\n\n\n// Encryption utilities\nconst ENCRYPTION_KEY = process.env.OAUTH_ENCRYPTION_KEY || process.env.ROKEY_ENCRYPTION_KEY || '';\nconst ALGORITHM = 'aes-256-gcm';\nfunction encrypt(text) {\n    if (!ENCRYPTION_KEY) {\n        throw new Error('Encryption key not configured');\n    }\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    const authTag = cipher.getAuthTag();\n    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;\n}\nfunction decrypt(encryptedText) {\n    if (!ENCRYPTION_KEY) {\n        throw new Error('Encryption key not configured');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted data format');\n    }\n    const iv = Buffer.from(parts[0], 'hex');\n    const authTag = Buffer.from(parts[1], 'hex');\n    const encrypted = parts[2];\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);\n    decipher.setAuthTag(authTag);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n// Store OAuth tokens securely\nasync function storeOAuthTokens(userId, toolType, tokenData) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        // Calculate expiration time\n        const expiresAt = tokenData.expires_in ? new Date(Date.now() + tokenData.expires_in * 1000) : null;\n        // Parse scopes\n        const scopes = tokenData.scope ? tokenData.scope.split(' ') : [];\n        // Encrypt sensitive data\n        const encryptedAccessToken = encrypt(tokenData.access_token);\n        const encryptedRefreshToken = tokenData.refresh_token ? encrypt(tokenData.refresh_token) : null;\n        const { data, error } = await supabase.from('tool_oauth_connections').upsert({\n            user_id: userId,\n            tool_type: toolType,\n            access_token: encryptedAccessToken,\n            refresh_token: encryptedRefreshToken,\n            expires_at: expiresAt?.toISOString(),\n            scopes,\n            provider_user_id: tokenData.provider_user_id,\n            provider_user_email: tokenData.provider_user_email,\n            provider_user_name: tokenData.provider_user_name,\n            connection_status: 'connected',\n            last_used_at: new Date().toISOString()\n        }, {\n            onConflict: 'user_id,tool_type'\n        }).select().single();\n        if (error) {\n            console.error('Error storing OAuth tokens:', error);\n            return null;\n        }\n        return {\n            ...data,\n            access_token: tokenData.access_token,\n            refresh_token: tokenData.refresh_token,\n            expires_at: expiresAt,\n            created_at: new Date(data.created_at),\n            updated_at: new Date(data.updated_at)\n        };\n    } catch (error) {\n        console.error('Error in storeOAuthTokens:', error);\n        return null;\n    }\n}\n// Retrieve OAuth tokens for a user and tool\nasync function getOAuthTokens(userId, toolType) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { data, error } = await supabase.from('tool_oauth_connections').select('*').eq('user_id', userId).eq('tool_type', toolType).eq('connection_status', 'connected').single();\n        if (error || !data) {\n            return null;\n        }\n        // Decrypt sensitive data\n        const accessToken = decrypt(data.access_token);\n        const refreshToken = data.refresh_token ? decrypt(data.refresh_token) : undefined;\n        return {\n            ...data,\n            access_token: accessToken,\n            refresh_token: refreshToken,\n            expires_at: data.expires_at ? new Date(data.expires_at) : undefined,\n            created_at: new Date(data.created_at),\n            updated_at: new Date(data.updated_at)\n        };\n    } catch (error) {\n        console.error('Error retrieving OAuth tokens:', error);\n        return null;\n    }\n}\n// Check if token needs refresh\nfunction needsTokenRefresh(tokenData) {\n    if (!tokenData.expires_at) {\n        return false; // No expiration info, assume it's valid\n    }\n    // Refresh if token expires within 5 minutes\n    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);\n    return tokenData.expires_at <= fiveMinutesFromNow;\n}\n// Refresh OAuth token\nasync function refreshOAuthToken(userId, toolType) {\n    try {\n        const tokenData = await getOAuthTokens(userId, toolType);\n        if (!tokenData || !tokenData.refresh_token) {\n            return {\n                success: false,\n                error: 'No refresh token available'\n            };\n        }\n        const config = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getOAuthConfigForTool)(toolType);\n        if (!config) {\n            return {\n                success: false,\n                error: 'OAuth configuration not found'\n            };\n        }\n        // Prepare refresh request\n        const refreshData = new URLSearchParams({\n            grant_type: 'refresh_token',\n            refresh_token: tokenData.refresh_token,\n            client_id: config.clientId,\n            client_secret: config.clientSecret\n        });\n        const response = await fetch(config.tokenUrl, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: refreshData\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Token refresh failed:', errorText);\n            return {\n                success: false,\n                error: 'Token refresh failed'\n            };\n        }\n        const refreshResult = await response.json();\n        // Store updated tokens\n        const updatedTokenData = await storeOAuthTokens(userId, toolType, {\n            access_token: refreshResult.access_token,\n            refresh_token: refreshResult.refresh_token || tokenData.refresh_token,\n            expires_in: refreshResult.expires_in,\n            scope: refreshResult.scope || tokenData.scopes.join(' '),\n            provider_user_id: tokenData.provider_user_id,\n            provider_user_email: tokenData.provider_user_email,\n            provider_user_name: tokenData.provider_user_name\n        });\n        if (!updatedTokenData) {\n            return {\n                success: false,\n                error: 'Failed to store refreshed tokens'\n            };\n        }\n        return {\n            success: true,\n            access_token: refreshResult.access_token,\n            refresh_token: refreshResult.refresh_token || tokenData.refresh_token,\n            expires_at: updatedTokenData.expires_at\n        };\n    } catch (error) {\n        console.error('Error refreshing OAuth token:', error);\n        return {\n            success: false,\n            error: 'Token refresh error'\n        };\n    }\n}\n// Get valid access token (with automatic refresh)\nasync function getValidAccessToken(userId, toolType) {\n    try {\n        let tokenData = await getOAuthTokens(userId, toolType);\n        if (!tokenData) {\n            return null;\n        }\n        // Check if token needs refresh\n        if (needsTokenRefresh(tokenData)) {\n            const refreshResult = await refreshOAuthToken(userId, toolType);\n            if (refreshResult.success && refreshResult.access_token) {\n                return refreshResult.access_token;\n            } else {\n                // Mark token as expired\n                await updateConnectionStatus(userId, toolType, 'expired');\n                return null;\n            }\n        }\n        // Update last used timestamp\n        await updateLastUsed(userId, toolType);\n        return tokenData.access_token;\n    } catch (error) {\n        console.error('Error getting valid access token:', error);\n        return null;\n    }\n}\n// Update connection status\nasync function updateConnectionStatus(userId, toolType, status) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { error } = await supabase.from('tool_oauth_connections').update({\n            connection_status: status\n        }).eq('user_id', userId).eq('tool_type', toolType);\n        return !error;\n    } catch (error) {\n        console.error('Error updating connection status:', error);\n        return false;\n    }\n}\n// Update last used timestamp\nasync function updateLastUsed(userId, toolType) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { error } = await supabase.from('tool_oauth_connections').update({\n            last_used_at: new Date().toISOString()\n        }).eq('user_id', userId).eq('tool_type', toolType);\n        return !error;\n    } catch (error) {\n        console.error('Error updating last used timestamp:', error);\n        return false;\n    }\n}\n// Revoke OAuth connection\nasync function revokeOAuthConnection(userId, toolType) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { error } = await supabase.from('tool_oauth_connections').delete().eq('user_id', userId).eq('tool_type', toolType);\n        return !error;\n    } catch (error) {\n        console.error('Error revoking OAuth connection:', error);\n        return false;\n    }\n}\n// Get all tool connections for a user\nasync function getUserToolConnections(userId) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { data, error } = await supabase.from('tool_oauth_connections').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        if (error || !data) {\n            return [];\n        }\n        return data.map((item)=>({\n                ...item,\n                access_token: '',\n                refresh_token: undefined,\n                expires_at: item.expires_at ? new Date(item.expires_at) : undefined,\n                created_at: new Date(item.created_at),\n                updated_at: new Date(item.updated_at)\n            }));\n    } catch (error) {\n        console.error('Error getting user tool connections:', error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/tokenManager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fstatus%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();