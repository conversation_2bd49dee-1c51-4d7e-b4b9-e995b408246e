/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/memory/stats/route";
exports.ids = ["app/api/memory/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmemory%2Fstats%2Froute&page=%2Fapi%2Fmemory%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmemory%2Fstats%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmemory%2Fstats%2Froute&page=%2Fapi%2Fmemory%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmemory%2Fstats%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_memory_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/memory/stats/route.ts */ \"(rsc)/./src/app/api/memory/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/memory/stats/route\",\n        pathname: \"/api/memory/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/memory/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\memory\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_memory_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmemory%2Fstats%2Froute&page=%2Fapi%2Fmemory%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmemory%2Fstats%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/memory/stats/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/memory/stats/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const memoryName = searchParams.get('memoryName');\n        const nodeId = searchParams.get('nodeId');\n        if (!memoryName || !nodeId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing memoryName or nodeId parameter'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies\n        });\n        // Get current user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Query memory entries for this specific memory instance\n        const { data: memoryEntries, error: queryError } = await supabase.from('workflow_memory').select('*').eq('memory_name', memoryName).eq('node_id', nodeId).eq('user_id', user.id);\n        if (queryError) {\n            console.error('Error querying memory stats:', queryError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch memory stats'\n            }, {\n                status: 500\n            });\n        }\n        // Calculate stats\n        const entriesCount = memoryEntries?.length || 0;\n        let totalSizeKB = 0;\n        let lastUpdate = 'Never';\n        if (memoryEntries && memoryEntries.length > 0) {\n            // Calculate total size\n            totalSizeKB = memoryEntries.reduce((total, entry)=>{\n                return total + (entry.metadata?.size_kb || 0);\n            }, 0);\n            // Find most recent update\n            const sortedEntries = memoryEntries.sort((a, b)=>new Date(b.metadata?.updated_at || b.created_at).getTime() - new Date(a.metadata?.updated_at || a.created_at).getTime());\n            if (sortedEntries[0]) {\n                const updateTime = new Date(sortedEntries[0].metadata?.updated_at || sortedEntries[0].created_at);\n                lastUpdate = updateTime.toLocaleTimeString();\n            }\n        }\n        // Format size\n        const totalSizeMB = totalSizeKB / 1024;\n        const formattedSize = totalSizeMB < 0.1 ? `${totalSizeKB.toFixed(1)}KB` : `${totalSizeMB.toFixed(1)}MB`;\n        const stats = {\n            entriesCount,\n            totalSize: formattedSize,\n            lastUpdate\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats);\n    } catch (error) {\n        console.error('Memory stats API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/memory/stats/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/set-cookie-parser","vendor-chunks/jose"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmemory%2Fstats%2Froute&page=%2Fapi%2Fmemory%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmemory%2Fstats%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();