"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/lib/oauth/config.ts":
/*!*********************************!*\
  !*** ./src/lib/oauth/config.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_DESCRIPTIONS: () => (/* binding */ TOOL_DESCRIPTIONS),\n/* harmony export */   TOOL_DISPLAY_NAMES: () => (/* binding */ TOOL_DISPLAY_NAMES),\n/* harmony export */   TOOL_EMOJIS: () => (/* binding */ TOOL_EMOJIS),\n/* harmony export */   TOOL_ICONS: () => (/* binding */ TOOL_ICONS),\n/* harmony export */   generateAuthUrl: () => (/* binding */ generateAuthUrl),\n/* harmony export */   getOAuthConfigForTool: () => (/* binding */ getOAuthConfigForTool),\n/* harmony export */   getToolOAuthConfigs: () => (/* binding */ getToolOAuthConfigs),\n/* harmony export */   validateOAuthConfig: () => (/* binding */ validateOAuthConfig)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// OAuth Configuration for Tool Integrations\n// This file contains OAuth configurations for all supported tools\n// Get the base URL for redirects\nconst getBaseUrl = ()=>{\n    if (true) {\n        return window.location.origin;\n    }\n    // Server-side detection\n    if (false) {}\n    return process.env.NEXTAUTH_URL || 'http://localhost:3000';\n};\n// Google OAuth configuration for tools\nconst getGoogleToolsConfig = ()=>({\n        clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',\n        clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',\n        authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n        tokenUrl: 'https://oauth2.googleapis.com/token',\n        scopes: [\n            'https://www.googleapis.com/auth/drive',\n            'https://www.googleapis.com/auth/documents',\n            'https://www.googleapis.com/auth/spreadsheets',\n            'https://www.googleapis.com/auth/gmail.modify',\n            'https://www.googleapis.com/auth/calendar',\n            'https://www.googleapis.com/auth/youtube'\n        ],\n        redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || \"\".concat(getBaseUrl(), \"/api/auth/tools/google/callback\"),\n        additionalParams: {\n            access_type: 'offline',\n            prompt: 'consent',\n            include_granted_scopes: 'true'\n        }\n    });\n// Notion OAuth configuration\nconst getNotionConfig = ()=>({\n        clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',\n        clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',\n        authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',\n        tokenUrl: 'https://api.notion.com/v1/oauth/token',\n        scopes: [],\n        redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || \"\".concat(getBaseUrl(), \"/api/auth/tools/notion/callback\"),\n        additionalParams: {\n            owner: 'user',\n            response_type: 'code'\n        }\n    });\n// Tool-specific OAuth configurations\nconst getToolOAuthConfigs = ()=>{\n    const googleConfig = getGoogleToolsConfig();\n    return {\n        // Google services all use the same OAuth config with different scopes\n        google_drive: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/drive'\n            ]\n        },\n        google_docs: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/documents'\n            ]\n        },\n        google_sheets: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/spreadsheets'\n            ]\n        },\n        gmail: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/gmail.modify'\n            ]\n        },\n        calendar: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/calendar'\n            ]\n        },\n        youtube: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/youtube'\n            ]\n        },\n        notion: getNotionConfig(),\n        supabase: {\n            clientId: '',\n            clientSecret: '',\n            authorizationUrl: '',\n            tokenUrl: '',\n            scopes: [],\n            redirectUri: '',\n            additionalParams: {}\n        }\n    };\n};\n// Get OAuth config for a specific tool\nconst getOAuthConfigForTool = (toolType)=>{\n    const configs = getToolOAuthConfigs();\n    return configs[toolType] || null;\n};\n// Validate OAuth configuration\nconst validateOAuthConfig = (config)=>{\n    return !!(config.clientId && config.clientSecret && config.authorizationUrl && config.tokenUrl && config.redirectUri);\n};\n// Generate OAuth authorization URL\nconst generateAuthUrl = (toolType, state)=>{\n    const config = getOAuthConfigForTool(toolType);\n    if (!config || !validateOAuthConfig(config)) {\n        return null;\n    }\n    const params = new URLSearchParams({\n        client_id: config.clientId,\n        redirect_uri: config.redirectUri,\n        response_type: 'code',\n        scope: config.scopes.join(' '),\n        state,\n        ...config.additionalParams\n    });\n    return \"\".concat(config.authorizationUrl, \"?\").concat(params.toString());\n};\n// Tool display names\nconst TOOL_DISPLAY_NAMES = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    google_sheets: 'Google Sheets',\n    gmail: 'Gmail',\n    calendar: 'Google Calendar',\n    youtube: 'YouTube',\n    notion: 'Notion',\n    supabase: 'Supabase'\n};\n// Tool icons - Professional logos from reliable sources\nconst TOOL_ICONS = {\n    google_drive: 'https://cloud.gmelius.com/public/logos/google/Google_Drive_Logo.svg',\n    google_docs: 'https://cloud.gmelius.com/public/logos/google/Google_Docs_Logo.svg',\n    google_sheets: 'https://cloud.gmelius.com/public/logos/google/Google_Sheets_Logo.svg',\n    gmail: 'https://cloud.gmelius.com/public/logos/google/Gmail_Logo.svg',\n    calendar: 'https://cloud.gmelius.com/public/logos/google/Google_Calendar_Logo.svg',\n    youtube: 'https://kstatic.googleusercontent.com/files/10450be01c1b184ffd2f49ede02f92c666f53fdf1b1cb6fa479f5e9d41cceb905e928c7be4f5593ff9edd0213a6cb096792e66ae17270b01e2cb909ee23a2955',\n    notion: 'https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/notion.svg',\n    supabase: 'https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/supabase.svg'\n};\n// Tool emoji fallbacks for places where images can't be used (like select options)\nconst TOOL_EMOJIS = {\n    google_drive: '📁',\n    google_docs: '📄',\n    google_sheets: '📊',\n    gmail: '📧',\n    calendar: '📅',\n    youtube: '📺',\n    notion: '📝',\n    supabase: '🗄️'\n};\n// Tool descriptions\nconst TOOL_DESCRIPTIONS = {\n    google_drive: 'Access and manage Google Drive files',\n    google_docs: 'Create and edit Google Documents',\n    google_sheets: 'Work with Google Spreadsheets',\n    gmail: 'Send and manage emails',\n    calendar: 'Manage calendar events and schedules',\n    youtube: 'Access YouTube data and analytics',\n    notion: 'Access Notion databases and pages',\n    supabase: 'Direct database operations'\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/oauth/config.ts\n"));

/***/ })

});