"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/MemoryNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemoryNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleStackIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MemoryNode(param) {\n    let { data, id } = param;\n    _s();\n    const config = data.config;\n    const memoryName = config === null || config === void 0 ? void 0 : config.memoryName;\n    const maxSize = (config === null || config === void 0 ? void 0 : config.maxSize) || 10240; // 10MB default\n    const encryption = (config === null || config === void 0 ? void 0 : config.encryption) !== false; // Default true\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges)();\n    // Find connected nodes\n    const connectedNodes = edges.filter((edge)=>edge.source === id).map((edge)=>edge.target);\n    const [memoryStats, setMemoryStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MemoryNode.useEffect\": ()=>{\n            // Fetch real memory stats from MemoryService\n            const fetchMemoryStats = {\n                \"MemoryNode.useEffect.fetchMemoryStats\": async ()=>{\n                    if (!memoryName) {\n                        setMemoryStats(null);\n                        setIsLoading(false);\n                        return;\n                    }\n                    setIsLoading(true);\n                    try {\n                        const response = await fetch(\"/api/memory/stats?memoryName=\".concat(encodeURIComponent(memoryName), \"&nodeId=\").concat(id));\n                        if (response.ok) {\n                            const stats = await response.json();\n                            setMemoryStats(stats);\n                        } else {\n                            // No data exists yet - show empty state\n                            setMemoryStats({\n                                entriesCount: 0,\n                                totalSize: '0MB',\n                                lastUpdate: 'Never'\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch memory stats:', error);\n                        // Show empty state on error\n                        setMemoryStats({\n                            entriesCount: 0,\n                            totalSize: '0MB',\n                            lastUpdate: 'Never'\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"MemoryNode.useEffect.fetchMemoryStats\"];\n            fetchMemoryStats();\n        }\n    }[\"MemoryNode.useEffect\"], [\n        memoryName,\n        maxSize,\n        id\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#ec4899\",\n        hasInput: false,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: memoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: [\n                            \"\\uD83E\\uDDE0 \",\n                            memoryName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Max: \",\n                            Math.round(maxSize / 1024),\n                            \"MB | \",\n                            encryption ? '🔒 Encrypted' : '🔓 Plain'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 bg-gray-700/30 px-2 py-1 rounded\",\n                        children: \"\\uD83D\\uDD04 Loading memory stats...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, this) : memoryStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    memoryStats.entriesCount,\n                                    \" entries\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCBE \",\n                                    memoryStats.totalSize,\n                                    \" used\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 17\n                            }, this),\n                            memoryStats.lastUpdate !== 'Never' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDD52 Updated \",\n                                    memoryStats.lastUpdate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 bg-gray-700/30 px-2 py-1 rounded\",\n                        children: \"\\uD83D\\uDCBE Empty - No data stored yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 15\n                    }, this),\n                    connectedNodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"\\uD83D\\uDD17 Connected to \",\n                            connectedNodes.length,\n                            \" node\",\n                            connectedNodes.length > 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded\",\n                        children: \"✅ Active & Ready\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 83,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"\\uD83E\\uDDE0 Plug & Play Memory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 121,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(MemoryNode, \"j4mGYxGe5NuOuAMPolrMUGYZl6A=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges\n    ];\n});\n_c = MemoryNode;\nvar _c;\n$RefreshReg$(_c, \"MemoryNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\n"));

/***/ })

});