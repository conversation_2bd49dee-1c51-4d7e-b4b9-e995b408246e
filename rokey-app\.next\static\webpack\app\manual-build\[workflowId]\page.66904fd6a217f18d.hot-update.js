"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/MemoryNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemoryNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleStackIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MemoryNode(param) {\n    let { data, id } = param;\n    _s();\n    const config = data.config;\n    const memoryName = config === null || config === void 0 ? void 0 : config.memoryName;\n    const maxSize = (config === null || config === void 0 ? void 0 : config.maxSize) || 10240; // 10MB default\n    const encryption = (config === null || config === void 0 ? void 0 : config.encryption) !== false; // Default true\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges)();\n    // Find connected nodes\n    const connectedNodes = edges.filter((edge)=>edge.source === id).map((edge)=>edge.target);\n    const [memoryStats, setMemoryStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MemoryNode.useEffect\": ()=>{\n            // Fetch real memory stats from MemoryService\n            const fetchMemoryStats = {\n                \"MemoryNode.useEffect.fetchMemoryStats\": async ()=>{\n                    if (!memoryName) {\n                        setMemoryStats(null);\n                        setIsLoading(false);\n                        return;\n                    }\n                    setIsLoading(true);\n                    try {\n                        const response = await fetch(\"/api/memory/stats?memoryName=\".concat(encodeURIComponent(memoryName), \"&nodeId=\").concat(id));\n                        if (response.ok) {\n                            const stats = await response.json();\n                            setMemoryStats(stats);\n                        } else {\n                            // No data exists yet - show empty state\n                            setMemoryStats({\n                                entriesCount: 0,\n                                totalSize: '0MB',\n                                lastUpdate: 'Never'\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch memory stats:', error);\n                        // Show empty state on error\n                        setMemoryStats({\n                            entriesCount: 0,\n                            totalSize: '0MB',\n                            lastUpdate: 'Never'\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"MemoryNode.useEffect.fetchMemoryStats\"];\n            fetchMemoryStats();\n        }\n    }[\"MemoryNode.useEffect\"], [\n        memoryName,\n        maxSize,\n        id\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#ec4899\",\n        hasInput: false,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: memoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: [\n                            \"\\uD83E\\uDDE0 \",\n                            memoryName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Max: \",\n                            Math.round(maxSize / 1024),\n                            \"MB | \",\n                            encryption ? '🔒 Encrypted' : '🔓 Plain'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, this),\n                    memoryStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    memoryStats.entriesCount,\n                                    \" entries\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCBE \",\n                                    memoryStats.totalSize,\n                                    \" used\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 17\n                            }, this),\n                            memoryStats.lastUpdate !== 'Never' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDD52 Updated \",\n                                    memoryStats.lastUpdate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 bg-gray-700/30 px-2 py-1 rounded\",\n                        children: \"\\uD83D\\uDCBE Empty - No data stored yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 15\n                    }, this),\n                    connectedNodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"\\uD83D\\uDD17 Connected to \",\n                            connectedNodes.length,\n                            \" node\",\n                            connectedNodes.length > 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded\",\n                        children: \"✅ Active & Ready\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 83,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"\\uD83E\\uDDE0 Plug & Play Memory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 117,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(MemoryNode, \"j4mGYxGe5NuOuAMPolrMUGYZl6A=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges\n    ];\n});\n_c = MemoryNode;\nvar _c;\n$RefreshReg$(_c, \"MemoryNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\n"));

/***/ })

});